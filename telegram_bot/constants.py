from datetime import timedelta
from typing import Final, get_args

from telegram_bot.typings import (
    AllowedConstantTenors,
    AllowedCurrencies,
    CommandType,
    WingSpreadKey,
)

# Base paths for configuration
BASE_SSM_PATH = "/config/telegram-bot/"
EXCHANGE_MAPPING_CONFIG_PATH = "/config/source_redirect_mapping"

# Default values
DEFAULT_EXCHANGE = "v2composite"
DEFAULT_SMILE_DELAY = timedelta(minutes=10)
# todo: remove delays when syn schedules are moved up
CURRENCY_SMILE_DELAYS = {
    "OP": timedelta(minutes=20),
    "ARB": timedelta(minutes=20),
}
# Links
BLOCKSCHOLES_RESEARCH_GROUP_LINK = "https://t.me/blockscholes"
BLOCKSCHOLES_RESEARCH_SIGNUP_LINK = "https://www.blockscholes.com/pricing"
PRIVACY_POLICY_LINK = "https://www.blockscholes.com/legal/privacy-policy"

# Command delays
COMMAND_DELAYS: dict[CommandType, timedelta] = {
    "price": timedelta(hours=6),
    "chart": timedelta(hours=24),
    "run": timedelta(hours=24),
}

# Supported currencies by exchange
DERIBIT_SUPPORTED_PRICE_CURRENCIES: list[AllowedCurrencies] = [
    "BTC",
    "ETH",
    "SOL",
    "XRP",
]

OKX_SUPPORTED_PRICE_CURRENCIES: list[AllowedCurrencies] = ["BTC", "ETH"]
OKX_SUPPORTED_VOL_CHART_CURRENCIES: list[AllowedCurrencies] = ["BTC", "ETH"]

DEFAULT_TENOR_TARGETS: list[AllowedConstantTenors] = [
    "3d",
    "7d",
    "14d",
    "30d",
    "60d",
]

DEFAULT_MAX_PRICING_CUTOFF = 120
CURRENCY_TO_MAX_PRICING_CUTOFF: dict[AllowedCurrencies, int] = {
    "BTC": 540,
    "ETH": 540,
}

CURRENCY_TO_TENOR_TARGETS: dict[
    AllowedCurrencies, list[AllowedConstantTenors]
] = {
    "BTC": list(get_args(AllowedConstantTenors)),
    "ETH": list(get_args(AllowedConstantTenors)),
}

BLOCKSCHOLES_TG_BOT_ADMIN_IDS = [5117077606, 5357884853]
BLOCKSCHOLES_TG_BOT_CONTACT_REQUEST_GROUP_ID = -4787379653

PERMISSIONED_SIGNAL_CHAT_IDS: list[int] = [
    -4660965885,  # Test chat - Andrew, Ahmad, Thahbib
    # -1002751159147,  # Research client - 1601
]

ONE_MONTH: int = 30
ONE_YEAR: int = 365

DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD = 2.5
# todo: parametrize per client - read from ssm
# Structure changed to map (currency, delta_key, tenor) -> threshold
DEFAULT_WING_SPREAD_TARGETS: Final[dict[WingSpreadKey, float]] = {
    ("ETH", "5delta", "180d"): DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD,
    ("ETH", "-5delta", "180d"): DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD,
    ("BTC", "5delta", "30d"): DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD,
    ("BTC", "-10delta", "180d"): DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD,
}

WING_SPREAD_CALC: Final = "WING_SPREAD_CALC"
