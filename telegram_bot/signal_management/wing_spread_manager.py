"""
Object-oriented wing spread signal management system.

This module provides classes for managing wing spread signals on a per-client basis,
including signal state tracking, threshold management, and client-specific configurations.
"""

import logging
from dataclasses import dataclass, field
from enum import Enum

from telegram_bot.constants import (
    DEFAULT_WING_SPREAD_TARGETS,
)
from telegram_bot.tg_bot_utils import (
    construct_wing_spread_key,
    get_wing_spread_threshold,
)
from telegram_bot.typings import ChartTimeseriesData, WingSpreadKey


class SignalState(Enum):
    """Enum representing the state of a signal relative to its threshold."""

    UNINITIALIZED = "uninitialized"
    BELOW_THRESHOLD = "below_threshold"
    ABOVE_THRESHOLD = "above_threshold"


@dataclass
class SignalCrossing:
    """Represents a signal crossing event."""

    signal_key: WingSpread<PERSON>ey
    crossed_above: bool
    value: float
    threshold: float
    previous_state: SignalState
    new_state: SignalState


@dataclass
class ClientSignalConfig:
    """Configuration for a specific client's signal preferences."""

    chat_id: int
    enabled_signals: set[WingSpreadKey] = field(default_factory=set)
    custom_thresholds: dict[WingSpreadKey, float] = field(default_factory=dict)

    def get_threshold(
        self, currency: str, delta_key: str, tenor: str
    ) -> float | None:
        """Get threshold for a signal, checking custom thresholds first."""
        wing_spread_key = (currency, delta_key, tenor)

        # Check custom threshold first
        if wing_spread_key in self.custom_thresholds:
            return self.custom_thresholds[wing_spread_key]

        # Fall back to default threshold
        return get_wing_spread_threshold(currency, delta_key, tenor)

    def is_signal_enabled(
        self, currency: str, delta_key: str, tenor: str
    ) -> bool:
        """Check if a signal is enabled for this client."""
        wing_spread_key = (currency, delta_key, tenor)

        # If no specific signals are configured, use default behavior
        if not self.enabled_signals:
            return (
                get_wing_spread_threshold(currency, delta_key, tenor)
                is not None
            )

        return wing_spread_key in self.enabled_signals


@dataclass
class ClientSignalState:
    """Tracks signal states for a specific client."""

    chat_id: int
    config: ClientSignalConfig
    signal_flags: dict[WingSpreadKey, SignalState] = field(default_factory=dict)

    def initialize_signal_state(
        self,
        signal_key: WingSpreadKey,
        is_above_threshold: bool,
        logger: logging.Logger,
    ) -> None:
        """
        Initialize a signal state without triggering crossing events.

        Args:
            signal_key: The signal identifier tuple (currency, delta_key, tenor)
            is_above_threshold: Whether the current value is above threshold
        """
        # Validate signal_key format
        if not isinstance(signal_key, tuple) or len(signal_key) != 3:
            logger.warning(
                f"Invalid signal key format during initialization: {signal_key}"
            )
            return

        initial_state = (
            SignalState.ABOVE_THRESHOLD
            if is_above_threshold
            else SignalState.BELOW_THRESHOLD
        )
        self.signal_flags[signal_key] = initial_state

        logger.info(
            f"Initialized signal {signal_key} to state {initial_state.value}"
        )

    def update_signal_state(
        self,
        signal_key: WingSpreadKey,
        is_above_threshold: bool,
        logger: logging.Logger,
        value: float,
        threshold: float,
    ) -> SignalCrossing | None:
        """
        Update signal state and return crossing event if one occurred.

        Args:
            signal_key: The signal identifier tuple (currency, delta_key, tenor)
            is_above_threshold: Whether the current value is above threshold

        Returns:
            SignalCrossing if a crossing occurred, None otherwise
        """
        previous_state = self.signal_flags.get(
            signal_key, SignalState.UNINITIALIZED
        )
        new_state = (
            SignalState.ABOVE_THRESHOLD
            if is_above_threshold
            else SignalState.BELOW_THRESHOLD
        )

        # If signal is uninitialized, initialize it first without triggering crossing
        if previous_state == SignalState.UNINITIALIZED:
            # todo: we should trigger to handle restarts durign periods where the signal has been triggered
            self.initialize_signal_state(signal_key, is_above_threshold, logger)
            return None

        self.signal_flags[signal_key] = new_state
        # Check for crossing - only trigger when transitioning between ABOVE/BELOW states
        if self._is_crossing_event(previous_state, new_state):
            return SignalCrossing(
                signal_key=signal_key,
                crossed_above=is_above_threshold,
                value=value,
                threshold=threshold,
                previous_state=previous_state,
                new_state=new_state,
            )

        return None

    def _is_crossing_event(
        self, previous_state: SignalState, new_state: SignalState
    ) -> bool:
        """
        Determine if a state transition represents a crossing event.

        Args:
            previous_state: The previous signal state
            new_state: The new signal state

        Returns:
            True if this represents a threshold crossing, False otherwise
        """
        # Only consider transitions between ABOVE and BELOW as crossings
        crossing_transitions = {
            (SignalState.BELOW_THRESHOLD, SignalState.ABOVE_THRESHOLD),
            (SignalState.ABOVE_THRESHOLD, SignalState.BELOW_THRESHOLD),
        }
        return (previous_state, new_state) in crossing_transitions

    def get_signal_state(self, signal_key: WingSpreadKey) -> SignalState:
        """
        Get the current state of a signal.

        Args:
            signal_key: The signal identifier tuple

        Returns:
            The current SignalState for the signal
        """
        return self.signal_flags.get(signal_key, SignalState.UNINITIALIZED)

    def is_signal_above_threshold(
        self, signal_key: WingSpreadKey
    ) -> bool | None:
        """
        Check if a signal is currently above threshold (backward compatibility).

        Args:
            signal_key: The signal identifier tuple

        Returns:
            True if above threshold, False if below, None if uninitialized
        """
        state = self.get_signal_state(signal_key)
        if state == SignalState.UNINITIALIZED:
            return None
        return state == SignalState.ABOVE_THRESHOLD


class WingSpreadSignalManager:
    """Manages wing spread signals for multiple clients. The assumption for this is that it enaled one bot to store
    signals for multiple clients. Another framework that can be adopted is to have a singleton instance of this class
    per bot, and associate each instance with a client/chat
    """

    def __init__(self, logger: logging.Logger, chat_id: int):
        self.logger = logger
        self._initialize_default_clients(chat_id=chat_id)

    def _initialize_default_clients(self, chat_id: int) -> None:
        """Initialize client states for all permissioned chat IDs."""
        self.client_state = ClientSignalState(
            chat_id=chat_id,
            config=create_default_client_config(chat_id=chat_id),
        )

    def process_signal_data(
        self,
        latest_values: dict[WingSpreadKey, float],
        logger: logging.Logger,
    ) -> list[SignalCrossing]:
        """
        Process signal data for all clients and return crossing events.

        Args:
            latest_values: Dictionary mapping signal keys to their latest values

        Returns:
            Dictionary mapping chat_id to list of crossing events for that client
        """

        client_crossings: list[SignalCrossing] = []

        for signal_key, value in latest_values.items():
            try:
                currency, delta_key, tenor = signal_key
            except ValueError:
                # Skip invalid signal key format
                continue

            # Check if this signal is enabled for this client
            if not self.client_state.config.is_signal_enabled(
                currency, delta_key, tenor
            ):
                continue

            # Get threshold for this client
            threshold = self.client_state.config.get_threshold(
                currency, delta_key, tenor
            )
            if threshold is None:
                continue

            # Check if value crosses threshold
            is_above_threshold = value >= threshold
            crossing = self.client_state.update_signal_state(
                signal_key=signal_key,
                is_above_threshold=is_above_threshold,
                logger=logger,
                value=value,
                threshold=threshold,
            )

            if crossing is not None:
                client_crossings.append(crossing)

        return client_crossings


def extract_latest_values(
    data: ChartTimeseriesData,
) -> dict[WingSpreadKey, float]:
    """
    Extract the latest values from chart timeseries data.

    Args:
        data: Chart timeseries data

    Returns:
        Dictionary mapping signal keys to their latest values
    """
    latest: dict[WingSpreadKey, float] = {}
    for key, series in data.items():
        currency, delta_key, tenor = key.split("_")
        wing_spread_key = construct_wing_spread_key(
            currency=currency,
            delta_key=delta_key,
            tenor=tenor,
        )
        if not series:
            continue
        try:
            newest = max(series, key=lambda x: x.get("timestamp", 0) or 0)
            latest[wing_spread_key] = float(newest["value"])
        except Exception:
            continue
    return latest


def create_default_client_config(chat_id: int) -> ClientSignalConfig:
    """
    Create a default client configuration with all available signals enabled.

    Args:
        chat_id: The client's chat ID

    Returns:
        ClientSignalConfig with default settings
    """
    config = ClientSignalConfig(chat_id=chat_id)

    # Enable all signals from DEFAULT_WING_SPREAD_TARGETS
    config.enabled_signals = set(DEFAULT_WING_SPREAD_TARGETS.keys())

    return config
