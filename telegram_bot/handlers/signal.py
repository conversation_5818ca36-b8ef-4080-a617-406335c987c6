import asyncio
import io
import logging
from typing import Any

from telegram import Update
from telegram.ext import ContextTypes

from telegram_bot.configs.bot_config import Botfig
from telegram_bot.constants import (
    PERMISSIONED_SIGNAL_CHAT_IDS,
)
from telegram_bot.exceptions import (
    LambdaResultDecodingError,
    MissingLambdaResponseError,
    RVDRequestConstructionError,
)
from telegram_bot.handlers.chart_builder import (
    build_rvd_request_event,
    get_timeseries_lookback,
)
from telegram_bot.handlers.charting import (
    call_lambda,
    get_decoded_images_and_data_from_response,
    handle_chart_type,
)
from telegram_bot.handlers.messaging import (
    send_failed_request_message,
)
from telegram_bot.handlers.validate import SignalValidator
from telegram_bot.image_utils import build_table_image
from telegram_bot.tg_bot_utils import get_wing_spread_threshold
from telegram_bot.typings import (
    ChartsAndData,
    ChartTimeseriesData,
    PlotObjects,
    SignalRow,
    SignalRowColors,
    ValidatedSignalChartRequest,
)


def _is_chat_id_permissioned_for_signal(chat_id: int | None) -> bool:
    """
    Check if a chat is blocked from using the signal command.

    Parameters:
    - chat_id: The Telegram chat ID

    Returns:
    - bool: True if chat is in PERMISSIONED_SIGNAL_CHAT_IDS, False otherwise
    """
    if chat_id is None:
        return False
    return chat_id in PERMISSIONED_SIGNAL_CHAT_IDS


def _process_signal_arguments(
    context: ContextTypes.DEFAULT_TYPE, botfig: Botfig
) -> ValidatedSignalChartRequest:
    return SignalValidator.validate(context.args or [], botfig)


async def handle_signal_request(
    update: Update,
    context: ContextTypes.DEFAULT_TYPE,
    botfig: Botfig,
    logger: logging.Logger,
    **kwargs: Any,
) -> None:
    """
    Handles the incoming signal request from a Telegram user.

    Validates user authorization, processes the signal request,
    invokes the appropriate AWS Lambda function to generate multiple charts,
    and sends all charts back to the user.

    Parameters:
    - update (Update): Incoming Telegram update that triggered the handler.
    - context (ContextTypes.DEFAULT_TYPE): Context provided by the Telegram bot.
    - botfig (Botfig): Bot configuration.
    - logger (logging.Logger): Logger instance.
    - **kwargs (Any): Additional keyword arguments.

    Returns:
    - None
    """

    if update.effective_chat is None or update.effective_user is None:
        logger.error("Effective User or Chat not found")
        return

    # Check if chat is permissioned
    if not _is_chat_id_permissioned_for_signal(update.effective_chat.id):
        logger.warning(
            f"Signal command is not persmissioned for chat {update.effective_chat.id}"
        )
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text="You are not authorized to use the /signal command in this chat. Please /contact a member of the BlockScholes team for access.",
        )
        return

    # Process and validate arguments
    try:

        signal_request = _process_signal_arguments(
            context=context, botfig=botfig
        )

    except Exception as _err:

        error_message = str(_err)
        logger.exception("Unexpected error occurred during argument processing")
        await send_failed_request_message(
            context,
            update,
            error=error_message,
            command_type="signal",
        )

        return

    try:
        if isinstance(signal_request, ValidatedSignalChartRequest):

            await _build_and_send_signal_charts(
                signal_request=signal_request,
                context=context,
                update=update,
                botfig=botfig,
                logger=logger,
            )

        else:
            raise NotImplementedError(
                "Only signal charts are currently supported"
            )
    except Exception as _err:
        error_message = str(_err)
        logger.exception(
            "Unexpected error occurred while building signal charts"
        )
        await send_failed_request_message(
            context,
            update,
            error=error_message,
            command_type="signal",
        )


def _create_signal_data_table_image(
    data: ChartTimeseriesData,
    logger: logging.Logger,
) -> io.BytesIO | None:
    """
    Create a PNG table summarizing the latest datapoint per metric from the Lambda 'data' object.

    For each key in `data`, selects the item with the maximum 'timestamp' and renders:
    - Signal
    - Value (rounded to 4 dp; text colored green/red vs threshold)
    - Threshold

    If no rows can be produced (e.g., no datapoints or missing thresholds), a 1x1 placeholder PNG is returned.
    Returns a BytesIO PNG image buffer.
    """
    BG_COLOR = "#101A2E"

    def _get_color(threshold: float | int, value: float | int) -> str:
        if value >= threshold:
            return "green"
        else:
            return "red"

    # Build rows with latest timestamp
    rows: list[SignalRow] = []
    color_map: list[SignalRowColors] = []
    for key, series in data.items():

        # todo: Is currently highly coupled with PlotTarget from researchVolDashbord
        currency, delta_key, tenor = key.split("_")
        try:

            latest = max(series, key=lambda x: x.get("timestamp", 0) or 0) or {}
            val = latest["value"]

            val_rounded = round(val, 4)

            # BTC_-10delta_180d -> BTC -10delta 180d
            signal_key = key.replace("_", " ")
            signal_threshold = get_wing_spread_threshold(
                currency=currency,
                delta_key=delta_key,
                tenor=tenor,
            )

            if signal_threshold is None:
                logger.error(
                    f"No threshold configured for {key}; skipping in table"
                )
                continue

            rows.append(
                SignalRow(
                    Signal=signal_key,
                    Value=val_rounded,
                    Threshold=signal_threshold,
                )
            )

            color_map.append(
                SignalRowColors(
                    Signal="white",
                    Value=_get_color(
                        threshold=signal_threshold,
                        value=val,
                    ),
                    Threshold="white",
                )
            )
        except Exception:
            logger.exception(f"Error processing signal data for {key}")
            continue

    # Sort by metric name for determinism
    order = sorted(range(len(rows)), key=lambda i: rows[i].get("Signal", ""))
    rows = [rows[i] for i in order]
    color_map = [color_map[i] for i in order]

    if not rows:
        return None
    # Use shared table image builder
    return build_table_image(
        rows,
        columns=["Signal", "Value", "Threshold"],
        bg_color=BG_COLOR,
        font_size=17,
        fig_width=16.0,
        row_height=0.5,
        edge_color="white",
        edge_linewidth=0.5,
        header_text_color="white",
        header_bold=True,
        dpi=150,
        data_text_color_map=color_map,
    )


async def _build_and_send_signal_charts(
    signal_request: ValidatedSignalChartRequest,
    context: ContextTypes.DEFAULT_TYPE,
    update: Update,
    botfig: Botfig,
    logger: logging.Logger,
) -> None:

    if update.effective_chat is None:
        return

    try:

        plot_objects = await handle_chart_type(
            chart_request=signal_request,
            botfig=botfig,
            context=context,
            update=update,
            apply_delay=False,  # currently no delay for signal charts as permissioning
            # is done on the signal command itself
        )
        assert plot_objects is not None

    except Exception as _err:

        error_message = str(_err)
        logger.exception("Error building signal plot objects")
        await send_failed_request_message(
            context,
            update,
            error=error_message,
            command_type="signal",
        )

        return

    try:
        decoded_images_and_data = await get_signal_charts_and_data(
            signal_request=signal_request,
            plot_objects=plot_objects,
            botfig=botfig,
            logger=logger,
        )
    except RVDRequestConstructionError as _err:
        error_message = str(_err)
        logger.exception("Error constructing chart request")
        await send_failed_request_message(
            context, update, error=error_message, command_type="signal"
        )
        return

    except MissingLambdaResponseError as _err:
        await send_failed_request_message(
            context, update, "No charts returned", command_type="signal"
        )
        return

    except LambdaResultDecodingError as _err:
        error_message = str(_err)
        logger.exception("Error parsing images")
        await send_failed_request_message(
            context, update, error=error_message, command_type="signal"
        )
        return

    except Exception as _err:
        error_message = str(_err)
        logger.exception("Unexpected error parsing images")
        await send_failed_request_message(
            context, update, error=error_message, command_type="signal"
        )
        return

    await send_signal_charts_and_data(
        decoded_images_and_data=decoded_images_and_data,
        context=context,
        chat_id=update.effective_chat.id,
        logger=logger,
    )


async def send_signal_charts_and_data(
    decoded_images_and_data: ChartsAndData,
    context: ContextTypes.DEFAULT_TYPE,
    chat_id: int,
    logger: logging.Logger,
) -> None:

    total_charts_sent = 0
    for _chart_category, charts in decoded_images_and_data.items():
        for chart_name, payload in charts.items():
            image_file = payload["image"]
            caption = f"{chart_name}"
            caption += "\n\nThis is charted with the latest available data."

            await context.bot.send_photo(
                chat_id=chat_id,
                photo=image_file,
                caption=caption,
            )

            # If data exists for this chart, build and send the table image immediately after
            chart_data = payload.get("data")
            if chart_data:
                try:
                    table_image = await asyncio.to_thread(
                        _create_signal_data_table_image,
                        data=chart_data,
                        logger=logger,
                    )

                    if table_image is None:
                        raise ValueError("No table image created")

                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=table_image,
                        caption=f"{chart_name} Data",
                    )
                except Exception:
                    logger.exception(
                        f"Failed to build or send latest datapoints table for {chart_name}"
                    )

            total_charts_sent += 1

    logger.info(f"Successfully sent {total_charts_sent} signal charts")


async def get_signal_charts_and_data(
    signal_request: ValidatedSignalChartRequest,
    plot_objects: PlotObjects,
    botfig: Botfig,
    logger: logging.Logger,
) -> ChartsAndData:
    try:
        request_event = await build_rvd_request_event(
            name="Signal",
            chart_request=signal_request,
            plot_objects=plot_objects,
            exchange="v2composite",
            apply_delay=False,  # currently no delay for signal charts as permissioning
            timeseries_lookback=get_timeseries_lookback(signal_request),
            timeseries_version=botfig.timeseries_version,
        )
    except Exception as _e:
        raise RVDRequestConstructionError from _e

    response_body = await call_lambda(request_event, logger)
    if not response_body:
        raise MissingLambdaResponseError("No charts returned")

    try:
        decoded_images_and_data = (
            await get_decoded_images_and_data_from_response(
                response_body, chart_type=signal_request.chart_type
            )
        )
    except Exception as _e:
        raise LambdaResultDecodingError from _e

    return decoded_images_and_data
