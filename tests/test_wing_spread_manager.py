"""Tests for the wing spread signal management system."""

import logging
from unittest.mock import Mock

import pytest

from telegram_bot.constants import PERMISSIONED_SIGNAL_CHAT_IDS
from telegram_bot.signal_management.wing_spread_manager import (
    ClientSignalConfig,
    ClientSignalState,
    WingSpreadSignalManager,
    create_default_client_config,
    extract_latest_values,
)
from telegram_bot.typings import ChartTimeseriesData


@pytest.fixture
def logger():
    """Create a mock logger for testing."""
    return Mock(spec=logging.Logger)


@pytest.fixture
def sample_timeseries_data() -> ChartTimeseriesData:
    """Create sample timeseries data for testing."""
    return {
        "BTC_5delta_30d": [
            {"timestamp": 1000, "value": 2.0},
            {"timestamp": 2000, "value": 3.0},  # Latest value
        ],
        "ETH_5delta_180d": [
            {"timestamp": 1000, "value": 1.5},
            {"timestamp": 2000, "value": 2.8},  # Latest value
        ],
        "ETH_-5delta_180d": [
            {"timestamp": 1000, "value": 3.0},
            {"timestamp": 2000, "value": 2.0},  # Latest value
        ],
    }


class TestExtractLatestValues:
    """Test the extract_latest_values function."""

    def test_extract_latest_values_success(self, sample_timeseries_data):
        """Test successful extraction of latest values."""
        result = extract_latest_values(sample_timeseries_data)

        expected = {
            "BTC_5delta_30d": 3.0,
            "ETH_5delta_180d": 2.8,
            "ETH_-5delta_180d": 2.0,
        }
        assert result == expected

    def test_extract_latest_values_empty_series(self):
        """Test handling of empty series."""
        data = {
            "BTC_5delta_30d": [],
            "ETH_5delta_180d": [{"timestamp": 1000, "value": 2.5}],
        }
        result = extract_latest_values(data)

        expected = {"ETH_5delta_180d": 2.5}
        assert result == expected

    def test_extract_latest_values_invalid_data(self):
        """Test handling of invalid data."""
        data = {
            "BTC_5delta_30d": [{"timestamp": 1000}],  # Missing value
            "ETH_5delta_180d": [
                {"timestamp": 1000, "value": "invalid"}
            ],  # Invalid value
            "valid_signal": [{"timestamp": 1000, "value": 2.5}],
        }
        result = extract_latest_values(data)

        expected = {"valid_signal": 2.5}
        assert result == expected


class TestClientSignalConfig:
    """Test the ClientSignalConfig class."""

    def test_get_threshold_custom(self):
        """Test getting custom threshold."""
        config = ClientSignalConfig(chat_id=123)
        config.custom_thresholds[("BTC", "5delta", "30d")] = 3.5

        threshold = config.get_threshold("BTC", "5delta", "30d")
        assert threshold == 3.5

    def test_get_threshold_default(self):
        """Test falling back to default threshold."""
        config = ClientSignalConfig(chat_id=123)

        # This should fall back to the default threshold lookup
        threshold = config.get_threshold("BTC", "5delta", "30d")
        assert threshold == 2.5  # DEFAULT_WING_SPREAD_SIGNAL_THRESHOLD

    def test_is_signal_enabled_with_specific_signals(self):
        """Test signal enablement with specific signals configured."""
        config = ClientSignalConfig(chat_id=123)
        config.enabled_signals.add(("BTC", "5delta", "30d"))

        assert config.is_signal_enabled("BTC", "5delta", "30d") is True
        assert config.is_signal_enabled("ETH", "5delta", "180d") is False

    def test_is_signal_enabled_default_behavior(self):
        """Test signal enablement with default behavior."""
        config = ClientSignalConfig(chat_id=123)
        # No specific signals configured, should use default behavior

        # These should return True if they have default thresholds
        assert config.is_signal_enabled("BTC", "5delta", "30d") is True
        assert config.is_signal_enabled("ETH", "5delta", "180d") is True


class TestClientSignalState:
    """Test the ClientSignalState class."""

    def test_update_signal_state_first_observation(self):
        """Test first observation doesn't trigger crossing."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        crossing = state.update_signal_state("BTC_5delta_30d", True)

        assert crossing is None
        assert state.signal_flags["BTC_5delta_30d"] is True

    def test_update_signal_state_crossing_above(self):
        """Test crossing above threshold."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        # Initialize state
        state.signal_flags["BTC_5delta_30d"] = False

        # Trigger crossing
        crossing = state.update_signal_state("BTC_5delta_30d", True)

        assert crossing is not None
        assert crossing.signal_key == "BTC_5delta_30d"
        assert crossing.currency == "BTC"
        assert crossing.delta_key == "5delta"
        assert crossing.tenor == "30d"
        assert crossing.crossed_above is True
        assert state.signal_flags["BTC_5delta_30d"] is True

    def test_update_signal_state_crossing_below(self):
        """Test crossing below threshold."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        # Initialize state
        state.signal_flags["BTC_5delta_30d"] = True

        # Trigger crossing
        crossing = state.update_signal_state("BTC_5delta_30d", False)

        assert crossing is not None
        assert crossing.crossed_above is False
        assert state.signal_flags["BTC_5delta_30d"] is False

    def test_update_signal_state_no_crossing(self):
        """Test no crossing when state doesn't change."""
        config = ClientSignalConfig(chat_id=123)
        state = ClientSignalState(chat_id=123, config=config)

        # Initialize state
        state.signal_flags["BTC_5delta_30d"] = True

        # No change
        crossing = state.update_signal_state("BTC_5delta_30d", True)

        assert crossing is None
        assert state.signal_flags["BTC_5delta_30d"] is True


class TestWingSpreadSignalManager:
    """Test the WingSpreadSignalManager class."""

    def test_initialization(self, logger):
        """Test manager initialization."""
        manager = WingSpreadSignalManager(logger)

        # Should have initialized clients for all permissioned chat IDs
        assert len(manager.client_states) == len(PERMISSIONED_SIGNAL_CHAT_IDS)
        for chat_id in PERMISSIONED_SIGNAL_CHAT_IDS:
            assert chat_id in manager.client_states

    def test_remove_client(self, logger):
        """Test removing a client."""
        manager = WingSpreadSignalManager(logger)
        chat_id = PERMISSIONED_SIGNAL_CHAT_IDS[0]

        manager.remove_client(chat_id)

        assert chat_id not in manager.client_states

    def test_process_signal_data(self, logger):
        """Test processing signal data for multiple clients."""
        manager = WingSpreadSignalManager(logger)

        # Set up test data
        latest_values = {
            "BTC_5delta_30d": 3.0,  # Above threshold (2.5)
            "ETH_5delta_180d": 2.0,  # Below threshold (2.5)
        }

        # Initialize some previous states to trigger crossings
        for client_state in manager.client_states.values():
            client_state.signal_flags["BTC_5delta_30d"] = (
                False  # Will cross above
            )
            client_state.signal_flags["ETH_5delta_180d"] = (
                True  # Will cross below
            )

        # Process the data
        all_crossings = manager.process_signal_data(latest_values, logger)

        # Should have crossings for all clients
        assert len(all_crossings) == len(PERMISSIONED_SIGNAL_CHAT_IDS)

        for chat_id, crossings in all_crossings.items():
            assert len(crossings) == 2  # Both signals should have crossed

            # Find the crossings
            btc_crossing = next(c for c in crossings if c.currency == "BTC")
            eth_crossing = next(c for c in crossings if c.currency == "ETH")

            assert btc_crossing.crossed_above is True
            assert eth_crossing.crossed_above is False


class TestConfigurationFunctions:
    """Test configuration-related functions."""

    def test_create_default_client_config(self):
        """Test creating default client configuration."""
        config = create_default_client_config(123)

        assert config.chat_id == 123
        assert (
            len(config.enabled_signals) > 0
        )  # Should have some default signals
        assert (
            len(config.custom_thresholds) == 0
        )  # No custom thresholds by default

    def test_load_client_configurations(self):
        """Test loading client configurations."""
        configs = load_client_configurations()

        # Should have configs for all permissioned chat IDs
        assert len(configs) == len(PERMISSIONED_SIGNAL_CHAT_IDS)
        for chat_id in PERMISSIONED_SIGNAL_CHAT_IDS:
            assert chat_id in configs
            assert configs[chat_id].chat_id == chat_id
